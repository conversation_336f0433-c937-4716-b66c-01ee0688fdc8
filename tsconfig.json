{
  "compilerOptions": {
    /* Language and Environment */
    "target": "ES2022",                                  /* Set the JavaScript language version for emitted JavaScript and include compatible library declarations. */
    "lib": ["ES2022"],                                   /* Specify a set of bundled library declaration files that describe the target runtime environment. */
    "experimentalDecorators": true,                      /* Enable experimental support for legacy experimental decorators. */
    "emitDecoratorMetadata": true,                       /* Emit design-type metadata for decorated declarations in source files. */
    "useDefineForClassFields": true,                     /* Emit ECMAScript-standard-compliant class fields. */

    /* Modules */
    "module": "CommonJS",                                /* Specify what module code is generated. */
    "rootDir": "./src",                                  /* Specify the root folder within your source files. */
    "moduleResolution": "node",                          /* Specify how TypeScript looks up a file from a given module specifier. */
    "baseUrl": "./",                                     /* Specify the base directory to resolve non-relative module names. */
    "resolveJsonModule": true,                           /* Enable importing .json files. */
    "allowSyntheticDefaultImports": true,                /* Allow 'import x from y' when a module doesn't have a default export. */
    "esModuleInterop": true,                             /* Emit additional JavaScript to ease support for importing CommonJS modules. */
    "forceConsistentCasingInFileNames": true,            /* Ensure that casing is correct in imports. */

    /* Emit */
    "outDir": "./dist",                                  /* Specify an output folder for all emitted files. */
    "removeComments": true,                              /* Disable emitting comments. */
    "declaration": true,                                 /* Generate .d.ts files from TypeScript and JavaScript files in your project. */
    "declarationMap": true,                              /* Create sourcemaps for d.ts files. */
    "sourceMap": true,                                   /* Create source map files for emitted JavaScript files. */

    /* Interop Constraints */
    "isolatedModules": true,                             /* Ensure that each file can be safely transpiled without relying on other imports. */
    "allowSyntheticDefaultImports": true,                /* Allow 'import x from y' when a module doesn't have a default export. */
    "esModuleInterop": true,                             /* Emit additional JavaScript to ease support for importing CommonJS modules. */

    /* Type Checking */
    "strict": true,                                      /* Enable all strict type-checking options. */
    "noImplicitAny": true,                               /* Enable error reporting for expressions and declarations with an implied 'any' type. */
    "strictNullChecks": true,                            /* When type checking, take into account 'null' and 'undefined'. */
    "strictFunctionTypes": true,                         /* When assigning functions, check to ensure parameters and the return values are subtype-compatible. */
    "strictBindCallApply": true,                         /* Check that the arguments for 'bind', 'call', and 'apply' methods match the original function. */
    "strictPropertyInitialization": true,                /* Check for class properties that are declared but not set in the constructor. */
    "noImplicitReturns": true,                           /* Enable error reporting for codepaths that do not explicitly return in a function. */
    "noFallthroughCasesInSwitch": true,                  /* Enable error reporting for fallthrough cases in switch statements. */
    "noUncheckedIndexedAccess": true,                    /* Add 'undefined' to a type when accessed using an index. */
    "noImplicitOverride": true,                          /* Ensure overriding members in derived classes are marked with an override modifier. */
    "allowUnusedLabels": false,                          /* Disable error reporting for unused labels. */
    "allowUnreachableCode": false,                       /* Disable error reporting for unreachable code. */

    /* Completeness */
    "skipDefaultLibCheck": true,                         /* Skip type checking .d.ts files that are included with TypeScript. */
    "skipLibCheck": true                                 /* Skip type checking all .d.ts files. */
  },
  "include": [
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts"
  ]
}
