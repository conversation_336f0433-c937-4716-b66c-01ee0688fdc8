interface ComplianceDocument {
    id: string;
    content: string;
    documentType: 'contract' | 'policy' | 'procedure' | 'financial_report';
    uploadedAt: Date;
    status: 'pending' | 'processing' | 'compliant' | 'non_compliant' | 'requires_review';
}
interface ComplianceRule {
    id: string;
    standard: 'SEC' | 'GLBA' | 'SOX' | 'GDPR' | 'CCPA';
    ruleType: 'data_protection' | 'financial_disclosure' | 'privacy' | 'security';
    description: string;
    pattern: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
}
interface KYCProfile {
    customerId: string;
    personalInfo: {
        name: string;
        dateOfBirth: string;
        ssn: string;
        address: string;
    };
    riskScore: number;
    status: 'pending' | 'approved' | 'rejected' | 'under_review';
    lastUpdated: Date;
}
interface ComplianceViolation {
    documentId: string;
    ruleId: string;
    violationType: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    recommendedAction: string;
    detectedAt: Date;
}
interface ComplianceReport {
    id: string;
    reportType: 'monthly' | 'quarterly' | 'annual' | 'incident';
    generatedAt: Date;
    compliance_rate: number;
    violations: ComplianceViolation[];
    recommendations: string[];
}
interface RegulatoryUpdate {
    id: string;
    standard: string;
    title: string;
    description: string;
    effectiveDate: Date;
    impact: 'low' | 'medium' | 'high';
    actionRequired: boolean;
}
export declare const app: import("express-serve-static-core").Express;
export declare class ComplianceSystem {
    static validateDocument(document: ComplianceDocument): Promise<boolean>;
    static scanForViolations(document: ComplianceDocument): Promise<ComplianceViolation[]>;
    static analyzeViolationContext(content: string, rule: ComplianceRule, matches: string[]): Promise<boolean>;
    static notifyComplianceTeam(violations: ComplianceViolation[]): Promise<void>;
    static verifyIdentity(profile: KYCProfile): Promise<{
        verified: boolean;
        confidence: number;
    }>;
    static performRiskAssessment(profile: KYCProfile): Promise<number>;
    static checkSanctionsList(profile: KYCProfile): Promise<{
        isListed: boolean;
        details?: string;
    }>;
    static fetchRegulatoryUpdates(): Promise<RegulatoryUpdate[]>;
    static analyzeRegulatoryImpact(updates: RegulatoryUpdate[]): Promise<string[]>;
    static generateComplianceMetrics(): Promise<{
        totalDocuments: number;
        compliantDocuments: number;
        violationsCount: number;
        complianceRate: number;
    }>;
    static formatComplianceReport(metrics: any, violations: ComplianceViolation[], recommendations: string[]): Promise<ComplianceReport>;
    static processComplianceDocument(document: ComplianceDocument): Promise<{
        status: string;
        violations: ComplianceViolation[];
    }>;
    static processKYCCustomer(profile: KYCProfile): Promise<{
        status: 'approved' | 'rejected' | 'under_review';
        riskScore: number;
        reasons: string[];
    }>;
    static generateComplianceReport(reportType: 'monthly' | 'quarterly' | 'annual'): Promise<ComplianceReport>;
    static weeklyRegulatoryMonitoring(scheduledTime: Date, startTime: Date): Promise<void>;
    static getRecommendedAction(rule: ComplianceRule): string;
}
export {};
//# sourceMappingURL=index.d.ts.map