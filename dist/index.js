"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComplianceSystem = exports.app = void 0;
const dbos_sdk_1 = require("@dbos-inc/dbos-sdk");
const express_1 = __importDefault(require("express"));
exports.app = (0, express_1.default)();
exports.app.use(express_1.default.json());
const complianceQueue = new dbos_sdk_1.WorkflowQueue("compliance_checks", {
    concurrency: 5,
    rateLimit: { limitPerPeriod: 100, periodSec: 60 }
});
const kycQueue = new dbos_sdk_1.WorkflowQueue("kyc_processing", {
    concurrency: 3,
    rateLimit: { limitPerPeriod: 50, periodSec: 60 }
});
const reportingQueue = new dbos_sdk_1.WorkflowQueue("report_generation", {
    concurrency: 2
});
const COMPLIANCE_RULES = [
    {
        id: "SEC-001",
        standard: "SEC",
        ruleType: "financial_disclosure",
        description: "Financial statements must include quarterly earnings disclosure",
        pattern: "quarterly.*(earnings|revenue|income)",
        severity: "high"
    },
    {
        id: "GLBA-001",
        standard: "GLBA",
        ruleType: "privacy",
        description: "Customer financial information must be protected",
        pattern: "(ssn|social.security|account.number|routing.number)",
        severity: "critical"
    },
    {
        id: "SOX-001",
        standard: "SOX",
        ruleType: "financial_disclosure",
        description: "Internal controls must be documented",
        pattern: "internal.control.*documentation",
        severity: "high"
    }
];
class ComplianceSystem {
    static async validateDocument(document) {
        dbos_sdk_1.DBOS.logger.info(`Validating document ${document.id}`);
        await dbos_sdk_1.DBOS.sleep(1000);
        if (!document.content || document.content.length < 100) {
            dbos_sdk_1.DBOS.logger.warn(`Document ${document.id} failed validation - insufficient content`);
            return false;
        }
        dbos_sdk_1.DBOS.logger.info(`Document ${document.id} validation completed`);
        return true;
    }
    static async scanForViolations(document) {
        dbos_sdk_1.DBOS.logger.info(`Scanning document ${document.id} for compliance violations`);
        const violations = [];
        await dbos_sdk_1.DBOS.sleep(2000);
        for (const rule of COMPLIANCE_RULES) {
            const regex = new RegExp(rule.pattern, 'gi');
            const matches = document.content.match(regex);
            if (matches) {
                const isViolation = await ComplianceSystem.analyzeViolationContext(document.content, rule, matches);
                if (isViolation) {
                    violations.push({
                        documentId: document.id,
                        ruleId: rule.id,
                        violationType: rule.ruleType,
                        description: `Potential ${rule.standard} violation: ${rule.description}`,
                        severity: rule.severity,
                        recommendedAction: ComplianceSystem.getRecommendedAction(rule),
                        detectedAt: new Date()
                    });
                }
            }
        }
        dbos_sdk_1.DBOS.logger.info(`Found ${violations.length} violations in document ${document.id}`);
        return violations;
    }
    static async analyzeViolationContext(content, rule, matches) {
        await dbos_sdk_1.DBOS.sleep(500);
        const contextWindow = 200;
        let hasViolation = false;
        for (const match of matches) {
            const matchIndex = content.indexOf(match);
            const context = content.substring(Math.max(0, matchIndex - contextWindow), Math.min(content.length, matchIndex + contextWindow + match.length));
            const protectivePatterns = [
                'encrypted', 'protected', 'secure', 'compliant',
                'privacy policy', 'data protection', 'authorized access'
            ];
            const hasProtection = protectivePatterns.some(pattern => context.toLowerCase().includes(pattern));
            if (!hasProtection && rule.severity === 'critical') {
                hasViolation = true;
                break;
            }
        }
        return hasViolation;
    }
    static async notifyComplianceTeam(violations) {
        dbos_sdk_1.DBOS.logger.info(`Notifying compliance team of ${violations.length} violations`);
        await dbos_sdk_1.DBOS.sleep(500);
        const criticalViolations = violations.filter(v => v.severity === 'critical');
        const highViolations = violations.filter(v => v.severity === 'high');
        if (criticalViolations.length > 0) {
            dbos_sdk_1.DBOS.logger.warn(`CRITICAL: ${criticalViolations.length} critical violations detected`);
        }
        if (highViolations.length > 0) {
            dbos_sdk_1.DBOS.logger.warn(`HIGH: ${highViolations.length} high-severity violations detected`);
        }
        dbos_sdk_1.DBOS.logger.info('Compliance team notifications sent');
    }
    static async verifyIdentity(profile) {
        dbos_sdk_1.DBOS.logger.info(`Verifying identity for customer ${profile.customerId}`);
        await dbos_sdk_1.DBOS.sleep(3000);
        const hasValidSSN = profile.personalInfo.ssn.length === 11;
        const hasValidDOB = new Date(profile.personalInfo.dateOfBirth) < new Date();
        const hasValidAddress = profile.personalInfo.address.length > 10;
        const confidence = (hasValidSSN ? 0.4 : 0) +
            (hasValidDOB ? 0.3 : 0) +
            (hasValidAddress ? 0.3 : 0);
        const verified = confidence >= 0.8;
        dbos_sdk_1.DBOS.logger.info(`Identity verification completed: ${verified ? 'PASSED' : 'FAILED'} (${confidence})`);
        return { verified, confidence };
    }
    static async performRiskAssessment(profile) {
        dbos_sdk_1.DBOS.logger.info(`Performing risk assessment for customer ${profile.customerId}`);
        await dbos_sdk_1.DBOS.sleep(2000);
        let riskScore = 0;
        const age = new Date().getFullYear() - new Date(profile.personalInfo.dateOfBirth).getFullYear();
        if (age < 25)
            riskScore += 20;
        else if (age < 35)
            riskScore += 10;
        const highRiskZipPrefixes = ['900', '800', '700'];
        const zipCode = profile.personalInfo.address.match(/\d{5}/)?.[0];
        if (zipCode && highRiskZipPrefixes.some(prefix => zipCode.startsWith(prefix))) {
            riskScore += 30;
        }
        riskScore += Math.floor(Math.random() * 20);
        dbos_sdk_1.DBOS.logger.info(`Risk assessment completed: score ${riskScore}`);
        return Math.min(riskScore, 100);
    }
    static async checkSanctionsList(profile) {
        dbos_sdk_1.DBOS.logger.info(`Checking sanctions list for customer ${profile.customerId}`);
        await dbos_sdk_1.DBOS.sleep(1500);
        const sanctionedNames = ['John Doe', 'Jane Smith'];
        const isListed = sanctionedNames.includes(profile.personalInfo.name);
        const result = {
            isListed,
            details: isListed ? 'Found match in OFAC sanctions list' : undefined
        };
        dbos_sdk_1.DBOS.logger.info(`Sanctions check completed: ${isListed ? 'MATCH FOUND' : 'CLEAR'}`);
        return result;
    }
    static async fetchRegulatoryUpdates() {
        dbos_sdk_1.DBOS.logger.info('Fetching latest regulatory updates');
        await dbos_sdk_1.DBOS.sleep(2000);
        const updates = [
            {
                id: 'SEC-2024-001',
                standard: 'SEC',
                title: 'Updated Cybersecurity Disclosure Requirements',
                description: 'New requirements for cybersecurity incident reporting within 4 business days',
                effectiveDate: new Date('2024-12-01'),
                impact: 'high',
                actionRequired: true
            },
            {
                id: 'GLBA-2024-002',
                standard: 'GLBA',
                title: 'Enhanced Privacy Notice Requirements',
                description: 'Updated privacy notice requirements for financial institutions',
                effectiveDate: new Date('2024-11-15'),
                impact: 'medium',
                actionRequired: true
            }
        ];
        dbos_sdk_1.DBOS.logger.info(`Fetched ${updates.length} regulatory updates`);
        return updates;
    }
    static async analyzeRegulatoryImpact(updates) {
        dbos_sdk_1.DBOS.logger.info('Analyzing regulatory impact');
        await dbos_sdk_1.DBOS.sleep(1000);
        const recommendations = [];
        for (const update of updates) {
            if (update.actionRequired) {
                switch (update.impact) {
                    case 'high':
                        recommendations.push(`URGENT: Review and update policies for ${update.title}`);
                        recommendations.push(`URGENT: Train compliance team on ${update.standard} changes`);
                        break;
                    case 'medium':
                        recommendations.push(`PRIORITY: Update procedures for ${update.title}`);
                        break;
                    case 'low':
                        recommendations.push(`MONITOR: Track implementation of ${update.title}`);
                        break;
                }
            }
        }
        dbos_sdk_1.DBOS.logger.info(`Generated ${recommendations.length} recommendations`);
        return recommendations;
    }
    static async generateComplianceMetrics() {
        dbos_sdk_1.DBOS.logger.info('Generating compliance metrics');
        await dbos_sdk_1.DBOS.sleep(1000);
        const totalDocuments = 1000;
        const compliantDocuments = 850;
        const violationsCount = 150;
        const complianceRate = (compliantDocuments / totalDocuments) * 100;
        return {
            totalDocuments,
            compliantDocuments,
            violationsCount,
            complianceRate
        };
    }
    static async formatComplianceReport(metrics, violations, recommendations) {
        dbos_sdk_1.DBOS.logger.info('Formatting compliance report');
        await dbos_sdk_1.DBOS.sleep(500);
        const report = {
            id: `RPT-${Date.now()}`,
            reportType: 'monthly',
            generatedAt: new Date(),
            compliance_rate: metrics.complianceRate,
            violations: violations.slice(0, 10),
            recommendations
        };
        dbos_sdk_1.DBOS.logger.info(`Compliance report ${report.id} formatted`);
        return report;
    }
    static async processComplianceDocument(document) {
        dbos_sdk_1.DBOS.logger.info(`Starting compliance processing for document ${document.id}`);
        await dbos_sdk_1.DBOS.setEvent('processing_status', 'started');
        const isValid = await ComplianceSystem.validateDocument(document);
        if (!isValid) {
            await dbos_sdk_1.DBOS.setEvent('processing_status', 'failed_validation');
            return { status: 'invalid', violations: [] };
        }
        await dbos_sdk_1.DBOS.setEvent('processing_status', 'validation_passed');
        const violations = await ComplianceSystem.scanForViolations(document);
        await dbos_sdk_1.DBOS.setEvent('violations_found', violations.length);
        if (violations.length > 0) {
            await ComplianceSystem.notifyComplianceTeam(violations);
            await dbos_sdk_1.DBOS.setEvent('processing_status', 'violations_reported');
        }
        await dbos_sdk_1.DBOS.setEvent('processing_status', 'completed');
        const status = violations.length > 0 ? 'non_compliant' : 'compliant';
        dbos_sdk_1.DBOS.logger.info(`Compliance processing completed for document ${document.id}: ${status}`);
        return { status, violations };
    }
    static async processKYCCustomer(profile) {
        dbos_sdk_1.DBOS.logger.info(`Starting KYC processing for customer ${profile.customerId}`);
        await dbos_sdk_1.DBOS.setEvent('kyc_status', 'identity_verification');
        const identityResult = await ComplianceSystem.verifyIdentity(profile);
        if (!identityResult.verified) {
            await dbos_sdk_1.DBOS.setEvent('kyc_status', 'identity_failed');
            return {
                status: 'rejected',
                riskScore: 100,
                reasons: ['Identity verification failed']
            };
        }
        await dbos_sdk_1.DBOS.setEvent('kyc_status', 'risk_assessment');
        const riskScore = await ComplianceSystem.performRiskAssessment(profile);
        await dbos_sdk_1.DBOS.setEvent('kyc_status', 'sanctions_check');
        const sanctionsResult = await ComplianceSystem.checkSanctionsList(profile);
        if (sanctionsResult.isListed) {
            await dbos_sdk_1.DBOS.setEvent('kyc_status', 'sanctions_match');
            return {
                status: 'rejected',
                riskScore: 100,
                reasons: [`Sanctions list match: ${sanctionsResult.details}`]
            };
        }
        let status;
        const reasons = [];
        if (riskScore >= 70) {
            status = 'under_review';
            reasons.push('High risk score requires manual review');
        }
        else if (riskScore >= 50) {
            status = 'under_review';
            reasons.push('Medium risk score requires additional verification');
        }
        else {
            status = 'approved';
            reasons.push('Low risk profile - automatically approved');
        }
        await dbos_sdk_1.DBOS.setEvent('kyc_status', 'completed');
        await dbos_sdk_1.DBOS.setEvent('final_status', status);
        dbos_sdk_1.DBOS.logger.info(`KYC processing completed for customer ${profile.customerId}: ${status}`);
        return { status, riskScore, reasons };
    }
    static async generateComplianceReport(reportType) {
        dbos_sdk_1.DBOS.logger.info(`Generating ${reportType} compliance report`);
        await dbos_sdk_1.DBOS.setEvent('report_status', 'metrics_generation');
        const metrics = await ComplianceSystem.generateComplianceMetrics();
        await dbos_sdk_1.DBOS.setEvent('report_status', 'regulatory_updates');
        const regulatoryUpdates = await ComplianceSystem.fetchRegulatoryUpdates();
        await dbos_sdk_1.DBOS.setEvent('report_status', 'impact_analysis');
        const recommendations = await ComplianceSystem.analyzeRegulatoryImpact(regulatoryUpdates);
        await dbos_sdk_1.DBOS.setEvent('report_status', 'formatting');
        const report = await ComplianceSystem.formatComplianceReport(metrics, [], recommendations);
        await dbos_sdk_1.DBOS.setEvent('report_status', 'completed');
        await dbos_sdk_1.DBOS.setEvent('report_id', report.id);
        dbos_sdk_1.DBOS.logger.info(`Compliance report ${report.id} generated successfully`);
        return report;
    }
    static async weeklyRegulatoryMonitoring(scheduledTime, startTime) {
        dbos_sdk_1.DBOS.logger.info(`Starting weekly regulatory monitoring at ${scheduledTime}`);
        const updates = await ComplianceSystem.fetchRegulatoryUpdates();
        const recommendations = await ComplianceSystem.analyzeRegulatoryImpact(updates);
        await dbos_sdk_1.DBOS.setEvent('weekly_updates_count', updates.length);
        await dbos_sdk_1.DBOS.setEvent('weekly_recommendations', recommendations);
        dbos_sdk_1.DBOS.logger.info(`Weekly regulatory monitoring completed - ${updates.length} updates processed`);
    }
    static getRecommendedAction(rule) {
        switch (rule.severity) {
            case 'critical':
                return 'Immediate remediation required - escalate to legal team';
            case 'high':
                return 'Priority remediation - update within 24 hours';
            case 'medium':
                return 'Schedule remediation within 1 week';
            case 'low':
                return 'Monitor and address in next review cycle';
            default:
                return 'Review and assess appropriate action';
        }
    }
}
exports.ComplianceSystem = ComplianceSystem;
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "validateDocument", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "scanForViolations", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Array]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "analyzeViolationContext", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "notifyComplianceTeam", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "verifyIdentity", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "performRiskAssessment", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "checkSanctionsList", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "fetchRegulatoryUpdates", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "analyzeRegulatoryImpact", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "generateComplianceMetrics", null);
__decorate([
    dbos_sdk_1.DBOS.step(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Array, Array]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "formatComplianceReport", null);
__decorate([
    dbos_sdk_1.DBOS.workflow(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "processComplianceDocument", null);
__decorate([
    dbos_sdk_1.DBOS.workflow(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "processKYCCustomer", null);
__decorate([
    dbos_sdk_1.DBOS.workflow(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "generateComplianceReport", null);
__decorate([
    dbos_sdk_1.DBOS.scheduled({ crontab: "0 9 * * 1" }),
    dbos_sdk_1.DBOS.workflow(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Date, Date]),
    __metadata("design:returntype", Promise)
], ComplianceSystem, "weeklyRegulatoryMonitoring", null);
exports.app.post('/api/compliance/document', async (req, res) => {
    try {
        const document = req.body;
        const handle = await dbos_sdk_1.DBOS.startWorkflow(ComplianceSystem, { queueName: complianceQueue.name }).processComplianceDocument(document);
        res.json({
            workflowId: handle.workflowID,
            status: 'processing_started',
            message: 'Document compliance check initiated'
        });
    }
    catch (error) {
        dbos_sdk_1.DBOS.logger.error(`Error processing document: ${error.message}`);
        res.status(500).json({ error: 'Internal server error' });
    }
});
exports.app.post('/api/kyc/customer', async (req, res) => {
    try {
        const profile = req.body;
        const handle = await dbos_sdk_1.DBOS.startWorkflow(ComplianceSystem, { queueName: kycQueue.name }).processKYCCustomer(profile);
        res.json({
            workflowId: handle.workflowID,
            status: 'kyc_processing_started',
            message: 'KYC verification initiated'
        });
    }
    catch (error) {
        dbos_sdk_1.DBOS.logger.error(`Error processing KYC: ${error.message}`);
        res.status(500).json({ error: 'Internal server error' });
    }
});
exports.app.post('/api/reports/generate', async (req, res) => {
    try {
        const { reportType } = req.body;
        if (!['monthly', 'quarterly', 'annual'].includes(reportType)) {
            return res.status(400).json({ error: 'Invalid report type' });
        }
        const handle = await dbos_sdk_1.DBOS.startWorkflow(ComplianceSystem, { queueName: reportingQueue.name }).generateComplianceReport(reportType);
        res.json({
            workflowId: handle.workflowID,
            status: 'report_generation_started',
            message: `${reportType} compliance report generation initiated`
        });
    }
    catch (error) {
        dbos_sdk_1.DBOS.logger.error(`Error generating report: ${error.message}`);
        res.status(500).json({ error: 'Internal server error' });
    }
});
exports.app.get('/api/workflow/:workflowId/status', async (req, res) => {
    try {
        const { workflowId } = req.params;
        const handle = await dbos_sdk_1.DBOS.retrieveWorkflow(workflowId);
        const status = await handle.getStatus();
        const events = {};
        try {
            events['processing_status'] = await dbos_sdk_1.DBOS.getEvent(workflowId, 'processing_status', 1);
            events['violations_found'] = await dbos_sdk_1.DBOS.getEvent(workflowId, 'violations_found', 1);
            events['kyc_status'] = await dbos_sdk_1.DBOS.getEvent(workflowId, 'kyc_status', 1);
            events['report_status'] = await dbos_sdk_1.DBOS.getEvent(workflowId, 'report_status', 1);
        }
        catch (eventError) {
            dbos_sdk_1.DBOS.logger.info(`No events found for workflow ${workflowId}`);
        }
        res.json({
            workflowId,
            status: status.status,
            workflowName: status.workflowName,
            events
        });
    }
    catch (error) {
        dbos_sdk_1.DBOS.logger.error(`Error getting workflow status: ${error.message}`);
        res.status(404).json({ error: 'Workflow not found' });
    }
});
exports.app.get('/api/workflow/:workflowId/result', async (req, res) => {
    try {
        const { workflowId } = req.params;
        const handle = await dbos_sdk_1.DBOS.retrieveWorkflow(workflowId);
        const result = await handle.getResult();
        res.json({
            workflowId,
            result
        });
    }
    catch (error) {
        dbos_sdk_1.DBOS.logger.error(`Error getting workflow result: ${error.message}`);
        res.status(404).json({ error: 'Workflow not found or not completed' });
    }
});
exports.app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        service: 'regulatory-compliance-system',
        timestamp: new Date().toISOString()
    });
});
async function main() {
    dbos_sdk_1.DBOS.setConfig({
        name: "regulatory-compliance-system",
        databaseUrl: process.env.DBOS_DATABASE_URL
    });
    await dbos_sdk_1.DBOS.launch({ expressApp: exports.app });
    const PORT = process.env.PORT || 3000;
    exports.app.listen(PORT, () => {
        console.log(`🏛️  Regulatory Compliance System running on http://localhost:${PORT}`);
        console.log(`📊 Compliance checking, KYC processing, and regulatory monitoring active`);
    });
}
main().catch(console.log);
//# sourceMappingURL=index.js.map